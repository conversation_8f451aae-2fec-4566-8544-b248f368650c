<script setup lang="ts">
import ProfileLayout from '@/layouts/ProfileLayout.vue';
import { User } from '@/types';
import {
    CalendarOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    EditOutlined,
    MailOutlined,
    PhoneOutlined,
    SaveOutlined,
    UploadOutlined,
    UserOutlined,
    WalletOutlined,
} from '@ant-design/icons-vue';
import { Head, useForm } from '@inertiajs/vue3';
import { message } from 'ant-design-vue';
import { computed, h, ref } from 'vue';

interface Props {
    user: User & {
        phone?: string;
        date_of_birth?: string;
        address?: string;
        avatar?: string;
        settings?: any;
    };
    stats: {
        total_transactions: number;
        total_spent: number;
        total_exchanged: number;
        member_since: string;
    };
}

const props = defineProps<Props>();

const isEditing = ref(false);
const avatarFileList = ref([]);

const form = useForm({
    name: props.user.name,
    email: props.user.email,
    phone: props.user.phone || '',
    date_of_birth: props.user.date_of_birth || '',
    address: props.user.address || '',
    avatar: null as File | null,
});

const avatarUrl = computed(() => {
    return (props.user as any).avatar_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(props.user.name)}&color=7F9CF5&background=EBF4FF`;
});

const handleEdit = () => {
    isEditing.value = true;
};

const handleCancel = () => {
    isEditing.value = false;
    form.reset();
    form.clearErrors();
    avatarFileList.value = [];
};

const handleSave = () => {
    form.put(route('profile.update'), {
        onSuccess: () => {
            isEditing.value = false;
            message.success('Thông tin cá nhân đã được cập nhật thành công!');
            avatarFileList.value = [];
        },
        onError: () => {
            message.error('Có lỗi xảy ra khi cập nhật thông tin!');
        },
    });
};

const handleAvatarChange = (info: any) => {
    const { fileList } = info;
    avatarFileList.value = fileList.slice(-1); // Keep only the last file
    
    if (fileList.length > 0) {
        const file = fileList[0].originFileObj;
        form.avatar = file;
    } else {
        form.avatar = null;
    }
};

const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
        message.error('Chỉ có thể upload file hình ảnh!');
        return false;
    }
    
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
        message.error('Kích thước file phải nhỏ hơn 2MB!');
        return false;
    }
    
    return false; // Prevent auto upload
};

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND',
    }).format(amount);
};
</script>

<template>
    <Head title="Thông tin cá nhân" />
    
    <ProfileLayout>
        <div class="profile-info">
            <!-- Header -->
            <div class="profile-header">
                <div class="header-content">
                    <h2 class="page-title">
                        <UserOutlined class="title-icon" />
                        Thông tin cá nhân
                    </h2>
                    <div class="header-actions">
                        <a-button
                            v-if="!isEditing"
                            type="primary"
                            @click="handleEdit"
                            :icon="h(EditOutlined)"
                        >
                            Chỉnh sửa
                        </a-button>
                        <a-space v-else>
                            <a-button @click="handleCancel">
                                Hủy bỏ
                            </a-button>
                            <a-button
                                type="primary"
                                @click="handleSave"
                                :loading="form.processing"
                                :icon="h(SaveOutlined)"
                            >
                                Lưu thay đổi
                            </a-button>
                        </a-space>
                    </div>
                </div>
            </div>

            <div class="profile-body">
                <a-row :gutter="24">
                    <!-- Personal Information -->
                    <a-col :xs="24" :lg="16">
                        <a-card title="Thông tin cá nhân" class="info-card">
                            <a-form layout="vertical" :model="form">
                                <a-row :gutter="16">
                                    <a-col :span="24">
                                        <a-form-item label="Avatar">
                                            <div class="avatar-section">
                                                <a-avatar
                                                    :size="80"
                                                    :src="avatarUrl"
                                                    class="current-avatar"
                                                >
                                                    {{ user.name.charAt(0).toUpperCase() }}
                                                </a-avatar>
                                                <div v-if="isEditing" class="avatar-upload">
                                                    <a-upload
                                                        v-model:file-list="avatarFileList"
                                                        :before-upload="beforeUpload"
                                                        @change="handleAvatarChange"
                                                        accept="image/*"
                                                        :max-count="1"
                                                        list-type="picture"
                                                    >
                                                        <a-button :icon="h(UploadOutlined)">
                                                            Chọn ảnh mới
                                                        </a-button>
                                                    </a-upload>
                                                </div>
                                            </div>
                                        </a-form-item>
                                    </a-col>
                                    
                                    <a-col :xs="24" :sm="12">
                                        <a-form-item
                                            label="Họ và tên"
                                            :validate-status="form.errors.name ? 'error' : ''"
                                            :help="form.errors.name"
                                        >
                                            <a-input
                                                v-model:value="form.name"
                                                :disabled="!isEditing"
                                                placeholder="Nhập họ và tên"
                                                :prefix="h(UserOutlined)"
                                            />
                                        </a-form-item>
                                    </a-col>
                                    
                                    <a-col :xs="24" :sm="12">
                                        <a-form-item
                                            label="Email"
                                            :validate-status="form.errors.email ? 'error' : ''"
                                            :help="form.errors.email"
                                        >
                                            <a-input
                                                v-model:value="form.email"
                                                :disabled="!isEditing"
                                                placeholder="Nhập email"
                                                :prefix="h(MailOutlined)"
                                            />
                                        </a-form-item>
                                    </a-col>
                                    
                                    <a-col :xs="24" :sm="12">
                                        <a-form-item
                                            label="Số điện thoại"
                                            :validate-status="form.errors.phone ? 'error' : ''"
                                            :help="form.errors.phone"
                                        >
                                            <a-input
                                                v-model:value="form.phone"
                                                :disabled="!isEditing"
                                                placeholder="Nhập số điện thoại"
                                                :prefix="h(PhoneOutlined)"
                                            />
                                        </a-form-item>
                                    </a-col>
                                    
                                    <a-col :xs="24" :sm="12">
                                        <a-form-item
                                            label="Ngày sinh"
                                            :validate-status="form.errors.date_of_birth ? 'error' : ''"
                                            :help="form.errors.date_of_birth"
                                        >
                                            <a-date-picker
                                                v-model:value="form.date_of_birth"
                                                :disabled="!isEditing"
                                                placeholder="Chọn ngày sinh"
                                                format="DD/MM/YYYY"
                                                :prefix="h(CalendarOutlined)"
                                                style="width: 100%"
                                            />
                                        </a-form-item>
                                    </a-col>
                                    
                                    <a-col :span="24">
                                        <a-form-item
                                            label="Địa chỉ"
                                            :validate-status="form.errors.address ? 'error' : ''"
                                            :help="form.errors.address"
                                        >
                                            <a-textarea
                                                v-model:value="form.address"
                                                :disabled="!isEditing"
                                                placeholder="Nhập địa chỉ"
                                                :rows="3"
                                            />
                                        </a-form-item>
                                    </a-col>
                                </a-row>
                            </a-form>
                        </a-card>
                    </a-col>

                    <!-- Account Status & Stats -->
                    <a-col :xs="24" :lg="8">
                        <!-- Account Status -->
                        <a-card title="Trạng thái tài khoản" class="status-card">
                            <div class="status-item">
                                <span class="status-label">Email:</span>
                                <a-tag
                                    :color="user.email_verified_at ? 'green' : 'orange'"
                                    :icon="user.email_verified_at ? h(CheckCircleOutlined) : h(CloseCircleOutlined)"
                                >
                                    {{ user.email_verified_at ? 'Đã xác thực' : 'Chưa xác thực' }}
                                </a-tag>
                            </div>
                            
                            <div class="status-item">
                                <span class="status-label">Số dư ví:</span>
                                <div class="wallet-balance">
                                    <WalletOutlined class="wallet-icon" />
                                    <span class="balance-amount">
                                        {{ formatCurrency((user as any).wallet_balance || 0) }}
                                    </span>
                                </div>
                            </div>
                            
                            <div class="status-item">
                                <span class="status-label">Thành viên từ:</span>
                                <span class="member-since">{{ stats.member_since }}</span>
                            </div>
                        </a-card>

                        <!-- Statistics -->
                        <a-card title="Thống kê" class="stats-card">
                            <div class="stat-item">
                                <div class="stat-value">{{ stats.total_transactions }}</div>
                                <div class="stat-label">Tổng giao dịch</div>
                            </div>
                            
                            <a-divider />
                            
                            <div class="stat-item">
                                <div class="stat-value">{{ formatCurrency(stats.total_spent) }}</div>
                                <div class="stat-label">Tổng chi tiêu</div>
                            </div>
                            
                            <a-divider />
                            
                            <div class="stat-item">
                                <div class="stat-value">{{ formatCurrency(stats.total_exchanged) }}</div>
                                <div class="stat-label">Tổng đổi thẻ</div>
                            </div>
                        </a-card>
                    </a-col>
                </a-row>
            </div>
        </div>
    </ProfileLayout>
</template>

<style scoped>
.profile-info {
    background: white;
}

.profile-header {
    padding: 24px;
    border-bottom: 1px solid #f0f0f0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-title {
    margin: 0;
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: 600;
    color: #262626;
}

.title-icon {
    margin-right: 8px;
    color: #1890ff;
}

.profile-body {
    padding: 24px;
}

.info-card,
.status-card,
.stats-card {
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.avatar-section {
    display: flex;
    align-items: center;
    gap: 16px;
}

.current-avatar {
    border: 2px solid #f0f0f0;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.status-item:last-child {
    margin-bottom: 0;
}

.status-label {
    font-weight: 500;
    color: #595959;
}

.wallet-balance {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #52c41a;
    font-weight: 600;
}

.wallet-icon {
    color: #52c41a;
}

.balance-amount {
    font-size: 16px;
}

.member-since {
    color: #262626;
    font-weight: 500;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: 600;
    color: #1890ff;
    margin-bottom: 4px;
}

.stat-label {
    color: #8c8c8c;
    font-size: 14px;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }

    .profile-body {
        padding: 16px;
    }

    .avatar-section {
        flex-direction: column;
        align-items: flex-start;
    }
}
</style>
