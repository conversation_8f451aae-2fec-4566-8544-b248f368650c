<script setup lang="ts">
import ProfileLayout from '@/layouts/ProfileLayout.vue';
import {
    CheckOutlined,
    EyeInvisibleOutlined,
    EyeOutlined,
    LockOutlined,
    SafetyOutlined,
} from '@ant-design/icons-vue';
import { Head, useForm } from '@inertiajs/vue3';
import { message } from 'ant-design-vue';
import { computed, ref } from 'vue';

const form = useForm({
    current_password: '',
    new_password: '',
    new_password_confirmation: '',
});

const showCurrentPassword = ref(false);
const showNewPassword = ref(false);
const showConfirmPassword = ref(false);

// Password strength calculation
const passwordStrength = computed(() => {
    const password = form.new_password;
    if (!password) return { score: 0, label: '', color: '' };

    let score = 0;
    const checks = {
        length: password.length >= 8,
        lowercase: /[a-z]/.test(password),
        uppercase: /[A-Z]/.test(password),
        numbers: /\d/.test(password),
        symbols: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    };

    score = Object.values(checks).filter(Boolean).length;

    const levels = [
        { score: 0, label: '', color: '' },
        { score: 1, label: 'Rất yếu', color: '#ff4d4f' },
        { score: 2, label: 'Yếu', color: '#ff7a45' },
        { score: 3, label: 'Trung bình', color: '#ffa940' },
        { score: 4, label: 'Mạnh', color: '#52c41a' },
        { score: 5, label: 'Rất mạnh', color: '#389e0d' },
    ];

    return levels[score] || levels[0];
});

const passwordChecks = computed(() => {
    const password = form.new_password;
    return [
        { label: 'Ít nhất 8 ký tự', valid: password.length >= 8 },
        { label: 'Có chữ thường', valid: /[a-z]/.test(password) },
        { label: 'Có chữ hoa', valid: /[A-Z]/.test(password) },
        { label: 'Có số', valid: /\d/.test(password) },
        { label: 'Có ký tự đặc biệt', valid: /[!@#$%^&*(),.?":{}|<>]/.test(password) },
    ];
});

const handleSubmit = () => {
    form.put(route('profile.change-password.update'), {
        onSuccess: () => {
            form.reset();
            message.success('Mật khẩu đã được thay đổi thành công!');
        },
        onError: () => {
            message.error('Có lỗi xảy ra khi thay đổi mật khẩu!');
        },
    });
};

const handleReset = () => {
    form.reset();
    form.clearErrors();
};
</script>

<template>
    <Head title="Đổi mật khẩu" />
    
    <ProfileLayout>
        <div class="change-password">
            <!-- Header -->
            <div class="page-header">
                <h2 class="page-title">
                    <LockOutlined class="title-icon" />
                    Đổi mật khẩu
                </h2>
                <p class="page-description">
                    Để bảo mật tài khoản, vui lòng sử dụng mật khẩu mạnh và không chia sẻ với người khác.
                </p>
            </div>

            <div class="page-content">
                <a-row :gutter="24">
                    <!-- Change Password Form -->
                    <a-col :xs="24" :lg="16">
                        <a-card title="Thay đổi mật khẩu" class="password-card">
                            <a-form
                                layout="vertical"
                                :model="form"
                                @finish="handleSubmit"
                            >
                                <!-- Current Password -->
                                <a-form-item
                                    label="Mật khẩu hiện tại"
                                    :validate-status="form.errors.current_password ? 'error' : ''"
                                    :help="form.errors.current_password"
                                >
                                    <a-input-password
                                        v-model:value="form.current_password"
                                        placeholder="Nhập mật khẩu hiện tại"
                                        size="large"
                                        :prefix="LockOutlined"
                                        :visibility-toggle="false"
                                    >
                                        <template #suffix>
                                            <component
                                                :is="showCurrentPassword ? EyeOutlined : EyeInvisibleOutlined"
                                                @click="showCurrentPassword = !showCurrentPassword"
                                                class="password-toggle"
                                            />
                                        </template>
                                    </a-input-password>
                                </a-form-item>

                                <!-- New Password -->
                                <a-form-item
                                    label="Mật khẩu mới"
                                    :validate-status="form.errors.new_password ? 'error' : ''"
                                    :help="form.errors.new_password"
                                >
                                    <a-input-password
                                        v-model:value="form.new_password"
                                        placeholder="Nhập mật khẩu mới"
                                        size="large"
                                        :prefix="LockOutlined"
                                        :visibility-toggle="false"
                                    >
                                        <template #suffix>
                                            <component
                                                :is="showNewPassword ? EyeOutlined : EyeInvisibleOutlined"
                                                @click="showNewPassword = !showNewPassword"
                                                class="password-toggle"
                                            />
                                        </template>
                                    </a-input-password>

                                    <!-- Password Strength Indicator -->
                                    <div v-if="form.new_password" class="password-strength">
                                        <div class="strength-bar">
                                            <div
                                                class="strength-fill"
                                                :style="{
                                                    width: `${(passwordStrength.score / 5) * 100}%`,
                                                    backgroundColor: passwordStrength.color,
                                                }"
                                            ></div>
                                        </div>
                                        <span
                                            class="strength-label"
                                            :style="{ color: passwordStrength.color }"
                                        >
                                            {{ passwordStrength.label }}
                                        </span>
                                    </div>
                                </a-form-item>

                                <!-- Confirm New Password -->
                                <a-form-item
                                    label="Xác nhận mật khẩu mới"
                                    :validate-status="form.errors.new_password_confirmation ? 'error' : ''"
                                    :help="form.errors.new_password_confirmation"
                                >
                                    <a-input-password
                                        v-model:value="form.new_password_confirmation"
                                        placeholder="Nhập lại mật khẩu mới"
                                        size="large"
                                        :prefix="LockOutlined"
                                        :visibility-toggle="false"
                                    >
                                        <template #suffix>
                                            <component
                                                :is="showConfirmPassword ? EyeOutlined : EyeInvisibleOutlined"
                                                @click="showConfirmPassword = !showConfirmPassword"
                                                class="password-toggle"
                                            />
                                        </template>
                                    </a-input-password>
                                </a-form-item>

                                <!-- Form Actions -->
                                <a-form-item>
                                    <a-space>
                                        <a-button
                                            type="primary"
                                            html-type="submit"
                                            size="large"
                                            :loading="form.processing"
                                        >
                                            <template #icon>
                                                <CheckOutlined />
                                            </template>
                                            Đổi mật khẩu
                                        </a-button>
                                        <a-button size="large" @click="handleReset">
                                            Hủy bỏ
                                        </a-button>
                                    </a-space>
                                </a-form-item>
                            </a-form>
                        </a-card>
                    </a-col>

                    <!-- Password Requirements -->
                    <a-col :xs="24" :lg="8">
                        <a-card title="Yêu cầu mật khẩu" class="requirements-card">
                            <div class="requirements-list">
                                <div
                                    v-for="check in passwordChecks"
                                    :key="check.label"
                                    class="requirement-item"
                                    :class="{ valid: check.valid }"
                                >
                                    <CheckOutlined
                                        v-if="check.valid"
                                        class="requirement-icon valid"
                                    />
                                    <span v-else class="requirement-icon invalid">•</span>
                                    <span class="requirement-text">{{ check.label }}</span>
                                </div>
                            </div>
                        </a-card>

                        <!-- Security Tips -->
                        <a-card title="Lời khuyên bảo mật" class="tips-card">
                            <div class="tips-list">
                                <div class="tip-item">
                                    <SafetyOutlined class="tip-icon" />
                                    <span>Không sử dụng mật khẩu đã dùng ở nơi khác</span>
                                </div>
                                <div class="tip-item">
                                    <SafetyOutlined class="tip-icon" />
                                    <span>Thay đổi mật khẩu định kỳ</span>
                                </div>
                                <div class="tip-item">
                                    <SafetyOutlined class="tip-icon" />
                                    <span>Không chia sẻ mật khẩu với ai</span>
                                </div>
                                <div class="tip-item">
                                    <SafetyOutlined class="tip-icon" />
                                    <span>Sử dụng trình quản lý mật khẩu</span>
                                </div>
                            </div>
                        </a-card>
                    </a-col>
                </a-row>
            </div>
        </div>
    </ProfileLayout>
</template>

<style scoped>
.change-password {
    background: white;
}

.page-header {
    padding: 24px;
    border-bottom: 1px solid #f0f0f0;
}

.page-title {
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: 600;
    color: #262626;
}

.title-icon {
    margin-right: 8px;
    color: #1890ff;
}

.page-description {
    margin: 0;
    color: #8c8c8c;
    font-size: 14px;
}

.page-content {
    padding: 24px;
}

.password-card,
.requirements-card,
.tips-card {
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.password-toggle {
    cursor: pointer;
    color: #8c8c8c;
}

.password-toggle:hover {
    color: #1890ff;
}

.password-strength {
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.strength-bar {
    flex: 1;
    height: 4px;
    background-color: #f0f0f0;
    border-radius: 2px;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    transition: all 0.3s ease;
}

.strength-label {
    font-size: 12px;
    font-weight: 500;
    min-width: 80px;
}

.requirements-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.requirement-item {
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.requirement-item.valid {
    color: #52c41a;
}

.requirement-icon {
    font-size: 14px;
}

.requirement-icon.valid {
    color: #52c41a;
}

.requirement-icon.invalid {
    color: #d9d9d9;
    font-weight: bold;
}

.requirement-text {
    font-size: 14px;
}

.tips-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.tip-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    font-size: 14px;
    color: #595959;
}

.tip-icon {
    color: #1890ff;
    margin-top: 2px;
    flex-shrink: 0;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .page-content {
        padding: 16px;
    }
}
</style>
