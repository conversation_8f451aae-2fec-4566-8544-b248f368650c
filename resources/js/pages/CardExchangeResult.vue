<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { CheckCircleOutlined, ClockCircleOutlined, CloseCircleOutlined, ExclamationCircleOutlined, SwapOutlined } from '@ant-design/icons-vue';
import { router } from '@inertiajs/vue3';
import { useEchoPublic } from '@laravel/echo-vue';
import { message } from 'ant-design-vue';
import { computed, onUnmounted, ref } from 'vue';

interface Transaction {
    id: number;
    status: string;
    card_type: {
        name: string;
        image_url: string;
    };
    denomination: {
        value: number;
    };
    serial: string;
    pin: string;
    declared_value: number;
    actual_value: number | null;
    exchange_rate: number;
    credited_amount: number | null;
    failure_reason: string | null;
    created_at: string;
    processed_at: string | null;
}

interface Props {
    transaction: Transaction;
}

const props = defineProps<Props>();

// Reactive transaction data
const transaction = ref<Transaction>({ ...props.transaction });
const isListening = ref(false);
let channel: any = null;

const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN').format(amount) + ' đ';
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('vi-VN');
};

const getStatusConfig = (status: string) => {
    switch (status) {
        case 'success':
            return {
                icon: CheckCircleOutlined,
                color: '#52c41a',
                text: 'Thành công',
                description: 'Thẻ cào đã được xử lý thành công và tiền đã được cộng vào ví của bạn.',
            };
        case 'pending':
            return {
                icon: ClockCircleOutlined,
                color: '#faad14',
                text: 'Đang xử lý',
                description: 'Thẻ cào đang được xử lý. Vui lòng chờ trong giây lát.',
            };
        case 'processing':
            return {
                icon: ClockCircleOutlined,
                color: '#1890ff',
                text: 'Đang xử lý',
                description: 'Thẻ cào đang được xử lý bởi nhà cung cấp. Vui lòng chờ trong giây lát.',
            };
        case 'failed':
            return {
                icon: CloseCircleOutlined,
                color: '#ff4d4f',
                text: 'Thất bại',
                description: 'Thẻ cào không thể xử lý được. Vui lòng kiểm tra lại thông tin.',
            };
        case 'invalid_card':
            return {
                icon: ExclamationCircleOutlined,
                color: '#ff7a45',
                text: 'Thẻ không hợp lệ',
                description: 'Thông tin thẻ cào không chính xác hoặc thẻ đã được sử dụng.',
            };
        default:
            return {
                icon: ClockCircleOutlined,
                color: '#d9d9d9',
                text: 'Không xác định',
                description: 'Trạng thái không xác định.',
            };
    }
};

const statusConfig = computed(() => getStatusConfig(transaction.value.status));

const maskCardInfo = (info: string) => {
    if (info.length <= 6) return info;
    return info.substring(0, 3) + '*'.repeat(info.length - 6) + info.substring(info.length - 3);
};

const { leaveChannel } = useEchoPublic<{
    status: string;
    actual_value: number;
    credited_amount: number;
    failure_reason: string;
    processed_at: string;
    message: string;
}>(`card-transactions.${transaction.value.id}`, 'CardTransactionStatusUpdated', (e) => {
    if (!['pending', 'processing'].includes(transaction.value.status)) {
        return;
    }

    transaction.value = {
        ...transaction.value,
        status: e.status,
        actual_value: e.actual_value,
        credited_amount: e.credited_amount,
        failure_reason: e.failure_reason,
        processed_at: e.processed_at,
    };

    if (e.message) {
        if (e.status === 'success') {
            message.success(e.message);
        } else if (e.status === 'failed' || e.status === 'invalid_card') {
            message.error(e.message);
        } else {
            message.info(e.message);
        }
    }
});

onUnmounted(() => {
    leaveChannel();
});
</script>

<template>
    <AppLayout>
        <div style="background: #f5f5f5; min-height: 100vh; padding: 24px 0">
            <div style="max-width: 800px; margin: 0 auto; padding: 0 16px">
                <a-card style="margin-bottom: 24px; text-align: center">
                    <component :is="statusConfig.icon" :style="{ fontSize: '64px', color: statusConfig.color, marginBottom: '16px' }" />
                    <h1 :style="{ color: statusConfig.color, marginBottom: '8px' }">
                        {{ statusConfig.text }}
                    </h1>
                    <p style="font-size: 16px; color: #666; margin-bottom: 0">
                        {{ statusConfig.description }}
                    </p>

                    <div v-if="transaction.status === 'pending' || transaction.status === 'processing'" style="margin-top: 16px">
                        <a-space>
                            <a-tag v-if="isListening" color="green">
                                <span style="display: inline-flex; align-items: center; gap: 4px">
                                    <span
                                        style="width: 6px; height: 6px; background: #52c41a; border-radius: 50%; animation: pulse 2s infinite"
                                    ></span>
                                    Đang theo dõi
                                </span>
                            </a-tag>
                        </a-space>
                    </div>
                </a-card>

                <a-card title="Chi tiết giao dịch" style="margin-bottom: 24px">
                    <a-descriptions :column="1" bordered>
                        <a-descriptions-item label="Mã giao dịch">
                            <a-tag color="blue">{{ transaction.id }}</a-tag>
                        </a-descriptions-item>

                        <a-descriptions-item label="Loại thẻ">
                            <a-space>
                                <img
                                    :src="transaction.card_type.image_url"
                                    :alt="transaction.card_type.name"
                                    style="width: 20px; height: 15px; object-fit: contain"
                                />
                                {{ transaction.card_type.name }}
                            </a-space>
                        </a-descriptions-item>

                        <a-descriptions-item label="Mệnh giá">
                            {{ formatCurrency(transaction.denomination.value) }}
                        </a-descriptions-item>

                        <a-descriptions-item label="Serial">
                            <a-typography-text code>{{ maskCardInfo(transaction.serial) }}</a-typography-text>
                        </a-descriptions-item>

                        <a-descriptions-item label="Mã PIN">
                            <a-typography-text code>{{ maskCardInfo(transaction.pin) }}</a-typography-text>
                        </a-descriptions-item>

                        <a-descriptions-item label="Tỷ lệ đổi"> {{ transaction.exchange_rate }}% </a-descriptions-item>

                        <a-descriptions-item v-if="transaction.actual_value" label="Giá trị thực tế">
                            {{ formatCurrency(transaction.actual_value) }}
                        </a-descriptions-item>

                        <a-descriptions-item v-if="transaction.credited_amount" label="Số tiền nhận được">
                            <span style="color: #52c41a; font-weight: 600; font-size: 16px">
                                {{ formatCurrency(transaction.credited_amount) }}
                            </span>
                        </a-descriptions-item>

                        <a-descriptions-item label="Thời gian gửi">
                            {{ formatDate(transaction.created_at) }}
                        </a-descriptions-item>

                        <a-descriptions-item v-if="transaction.processed_at" label="Thời gian xử lý">
                            {{ formatDate(transaction.processed_at) }}
                        </a-descriptions-item>

                        <a-descriptions-item v-if="transaction.failure_reason" label="Lý do thất bại">
                            <a-alert :message="transaction.failure_reason" type="error" show-icon style="margin: 0" />
                        </a-descriptions-item>
                    </a-descriptions>
                </a-card>

                <a-card
                    v-if="transaction.status === 'pending' || transaction.status === 'processing'"
                    title="Thông tin xử lý"
                    style="margin-bottom: 24px"
                >
                    <a-timeline>
                        <a-timeline-item color="green">
                            <template #dot>
                                <CheckCircleOutlined style="font-size: 16px" />
                            </template>
                            <div>
                                <strong>Đã nhận thẻ cào</strong>
                                <div style="color: #666; font-size: 12px">{{ formatDate(transaction.created_at) }}</div>
                            </div>
                        </a-timeline-item>

                        <a-timeline-item :color="transaction.status === 'processing' ? 'blue' : 'gray'">
                            <template #dot>
                                <ClockCircleOutlined style="font-size: 16px" />
                            </template>
                            <div>
                                <strong>Đang xử lý với nhà cung cấp</strong>
                                <div style="color: #666; font-size: 12px">
                                    {{ transaction.status === 'processing' ? 'Đang xử lý...' : 'Chờ xử lý' }}
                                </div>
                            </div>
                        </a-timeline-item>

                        <a-timeline-item color="gray">
                            <template #dot>
                                <CheckCircleOutlined style="font-size: 16px" />
                            </template>
                            <div>
                                <strong>Hoàn thành và cộng tiền vào ví</strong>
                                <div style="color: #666; font-size: 12px">Chờ xử lý</div>
                            </div>
                        </a-timeline-item>
                    </a-timeline>

                    <a-alert
                        message="Thời gian xử lý"
                        description="Thẻ cào thường được xử lý trong vòng 1-5 phút. Trong một số trường hợp đặc biệt, thời gian có thể kéo dài đến 30 phút."
                        type="info"
                        show-icon
                        style="margin-top: 16px"
                    />
                </a-card>

                <a-space style="width: 100%; justify-content: center" size="large">
                    <a-button type="primary" size="large" @click="router.get(route('card-exchange.index'))">
                        <SwapOutlined />
                        Đổi thẻ khác
                    </a-button>

                    <a-button size="large" @click="router.get(route('card-exchange.history'))"> Xem lịch sử </a-button>
                </a-space>
            </div>
        </div>
    </AppLayout>
</template>

<style scoped>
@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}
</style>
