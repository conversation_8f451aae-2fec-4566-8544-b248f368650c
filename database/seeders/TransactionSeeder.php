<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $user = \App\Models\User::first();

        if (!$user) {
            return;
        }

        $transactions = [
            [
                'user_id' => $user->id,
                'transaction_id' => 'TXN' . time() . '001',
                'type' => 'deposit',
                'amount' => 100000,
                'balance_before' => 0,
                'balance_after' => 100000,
                'status' => 'completed',
                'description' => 'Nạp tiền vào ví',
                'processed_at' => now(),
                'created_at' => now()->subDays(5),
                'updated_at' => now()->subDays(5),
            ],
            [
                'user_id' => $user->id,
                'transaction_id' => 'TXN' . time() . '002',
                'type' => 'purchase',
                'amount' => 50000,
                'balance_before' => 100000,
                'balance_after' => 50000,
                'status' => 'completed',
                'description' => 'Mua thẻ Viettel 50k',
                'processed_at' => now(),
                'created_at' => now()->subDays(3),
                'updated_at' => now()->subDays(3),
            ],
            [
                'user_id' => $user->id,
                'transaction_id' => 'TXN' . time() . '003',
                'type' => 'exchange',
                'amount' => 20000,
                'balance_before' => 50000,
                'balance_after' => 68000,
                'status' => 'completed',
                'description' => 'Đổi thẻ Mobifone 20k',
                'processed_at' => now(),
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subDays(1),
            ],
            [
                'user_id' => $user->id,
                'transaction_id' => 'TXN' . time() . '004',
                'type' => 'exchange',
                'amount' => 10000,
                'balance_before' => 68000,
                'balance_after' => 68000,
                'status' => 'pending',
                'description' => 'Đổi thẻ Vinaphone 10k',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($transactions as $transaction) {
            \App\Models\Transaction::create($transaction);
        }
    }
}
