<?php

namespace App\Http\Controllers;

use App\Models\CardTransaction;
use App\Services\CardExchangeService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;

class CardExchangeController extends Controller
{
    public function index(CardExchangeService $exchangeService)
    {
        $cardTypes = $exchangeService->getAvailableCardTypes();

        return Inertia::render('CardExchange', [
            'cardTypes' => $cardTypes,
        ]);
    }

    public function store(Request $request, CardExchangeService $exchangeService)
    {
        $validated = $request->validate([
            'telco' => 'required|string',
            'value' => 'required|integer|min:10000',
            'serial' => 'required|string|min:10|max:20|regex:/^[a-zA-Z0-9]+$/',
            'pin' => 'required|string|min:10|max:20|regex:/^[a-zA-Z0-9]+$/',
        ]);

        try {
            $transaction = $exchangeService->processExchange(Auth::user(), $validated);

            return redirect()
                ->route('card-exchange.show', $transaction->request_id)
                ->with('success', 'Thẻ cào đã được gửi xử lý thành công!');
        } catch (Exception $e) {
            throw ValidationException::withMessages([
                'general' => $e->getMessage(),
            ]);
        }
    }

    public function show(string $requestId)
    {
        $transaction = CardTransaction::query()
            ->whereBelongsTo(Auth::user())
            ->where('request_id', $requestId)
            ->first();

        if (! $transaction) {
            abort(404);
        }

        return Inertia::render('CardExchangeResult', [
            'transaction' => [
                'id' => $transaction->request_id,
                'status' => $transaction->status,
                'card_type' => [
                    'name' => $transaction->card_name ?? 'Unknown',
                    'image_url' => $this->getCardImageUrl($transaction->telco) ?? '',
                ],
                'denomination' => [
                    'value' => $transaction->declared_value,
                ],
                'serial' => $transaction->serial,
                'pin' => $transaction->pin,
                'declared_value' => $transaction->declared_value,
                'actual_value' => $transaction->actual_value,
                'exchange_rate' => $transaction->exchange_rate,
                'credited_amount' => $transaction->credited_amount,
                'failure_reason' => $transaction->failure_reason,
                'created_at' => $transaction->created_at,
                'processed_at' => $transaction->processed_at,
            ],
        ]);
    }

    private function getCardImageUrl(?string $telco): ?string
    {
        if (! $telco) {
            return null;
        }

        $images = [
            'VIETTEL' => 'card-type/viettel.png',
            'MOBIFONE' => 'card-type/mobifone.png',
            'VINAPHONE' => 'card-type/vinaphone.png',
            'VNMOBI' => 'card-type/vietnamobile.png',
            'GARENA' => 'card-type/garena.png',
            'ZING' => 'card-type/zing.png',
            'VCOIN' => 'card-type/vcoin.png',
            'GATE' => 'card-type/gate.png',
        ];

        return asset($images[$telco] ?? null);
    }

    public function history(Request $request)
    {
        $query = Auth::user()->cardTransactions()
            ->orderBy('created_at', 'desc');

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('from_date')) {
            $query->whereDate('created_at', '>=', $request->from_date);
        }

        if ($request->filled('to_date')) {
            $query->whereDate('created_at', '<=', $request->to_date);
        }

        $transactions = $query->paginate(20);

        $transactions->getCollection()->transform(function ($transaction) {
            return [
                'id' => $transaction->id,
                'request_id' => $transaction->request_id,
                'status' => $transaction->status,
                'card_type' => [
                    'name' => $transaction->card_name ?? 'Unknown',
                    'image_url' => $this->getCardImageUrl($transaction->telco) ?? '',
                ],
                'denomination' => [
                    'value' => $transaction->declared_value,
                ],
                'declared_value' => $transaction->declared_value,
                'actual_value' => $transaction->actual_value,
                'exchange_rate' => $transaction->exchange_rate,
                'credited_amount' => $transaction->credited_amount,
                'created_at' => $transaction->created_at,
                'processed_at' => $transaction->processed_at,
            ];
        });

        return Inertia::render('CardExchangeHistory', [
            'transactions' => $transactions,
            'filters' => $request->only(['status', 'from_date', 'to_date']),
            'totalAmount' => $transactions->getCollection()->sum('credited_amount'),
        ]);
    }
}
